{"rustc": 13226066032359371072, "features": "[\"default\", \"hooks\", \"html\", \"macro\", \"signals\", \"warnings\"]", "declared_features": "[\"default\", \"hooks\", \"html\", \"macro\", \"signals\", \"warnings\"]", "target": 13241787460562148162, "profile": 10809724437792986082, "path": 18309883819886361833, "deps": [[2415174312781903822, "dioxus_document", false, 11319478112553093572], [2589393185178292669, "dioxus_signals", false, 551041442789365249], [2833126123452420110, "dioxus_core", false, 3740970622420549154], [3405920678550729695, "dioxus_core_macro", false, 16561582413425617653], [7137988838644907815, "dioxus_hooks", false, 8980428815269320040], [7550334499606919131, "dioxus_html", false, 4891368746447150528], [8787103190345202991, "dioxus_config_macro", false, 16902175592322344552], [9441945037928527794, "dioxus_history", false, 2513230216999411273], [12495972900271040957, "dioxus_rsx", false, 7672447692995849651], [17098021663904997343, "warnings", false, 7445851980047213429]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/dioxus-lib-99468323a4c657eb/dep-lib-dioxus_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}