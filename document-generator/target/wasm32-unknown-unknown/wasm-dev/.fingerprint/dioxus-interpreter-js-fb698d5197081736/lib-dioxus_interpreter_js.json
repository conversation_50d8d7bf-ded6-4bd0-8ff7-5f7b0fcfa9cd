{"rustc": 13226066032359371072, "features": "[\"default\", \"minimal_bindings\", \"sledgehammer\", \"webonly\"]", "declared_features": "[\"binary-protocol\", \"default\", \"minimal_bindings\", \"serialize\", \"sledgehammer\", \"webonly\"]", "target": 2819413441274508901, "profile": 10809724437792986082, "path": 7365136449195884260, "deps": [[5695500057974507883, "sledgehammer_bindgen", false, 10901923630838984612], [6946689283190175495, "wasm_bindgen", false, 12506201801350629679], [8264480821543757363, "web_sys", false, 10147337267461757977], [9003359908906038687, "js_sys", false, 4381730769811674265], [11219889689178438061, "sledgehammer_utils", false, 9532241301140690818], [13776550934572173705, "build_script_build", false, 17507962856475950087], [15917073803248137067, "wasm_bindgen_futures", false, 14240984254262949917], [16055916053474393816, "rustc_hash", false, 6863483921615868662]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/dioxus-interpreter-js-fb698d5197081736/dep-lib-dioxus_interpreter_js", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}