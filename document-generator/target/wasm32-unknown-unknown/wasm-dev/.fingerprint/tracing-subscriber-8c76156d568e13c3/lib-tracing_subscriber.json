{"rustc": 13226066032359371072, "features": "[\"alloc\", \"registry\", \"sharded-slab\", \"std\", \"thread_local\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 12568277909261987777, "path": 5335209970007944021, "deps": [[1017461770342116999, "sharded_slab", false, 3076834269520246306], [1359731229228270592, "thread_local", false, 2463784180779572161], [3424551429995674438, "tracing_core", false, 16754574631954910708]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/tracing-subscriber-8c76156d568e13c3/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}