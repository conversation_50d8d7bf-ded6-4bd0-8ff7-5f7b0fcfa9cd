{"rustc": 13226066032359371072, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13776550934572173705, "build_script_build", false, 4102475363992176142]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/wasm-dev/build/dioxus-interpreter-js-cabc40824ab1ddc7/output", "paths": ["./src/ts/serialize.ts", "./src/ts/initialize_streaming.ts", "./src/ts/form.ts", "./src/ts/core.ts", "./src/ts/common.ts", "./src/ts/native.ts", "./src/ts/hydrate_types.ts", "./src/ts/set_attribute.ts", "./src/ts/patch_console.ts", "./src/ts/hydrate.ts"]}}], "rustflags": [], "config": 0, "compile_kind": 0}