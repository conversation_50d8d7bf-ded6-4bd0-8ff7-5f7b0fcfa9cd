{"rustc": 13226066032359371072, "features": "[\"web\"]", "declared_features": "[\"web\"]", "target": 11659636895779589644, "profile": 10809724437792986082, "path": 12343294781137759968, "deps": [[6946689283190175495, "wasm_bindgen", false, 12506201801350629679]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/dioxus-cli-config-adc0cfd56db1f36b/dep-lib-dioxus_cli_config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}