{"rustc": 13226066032359371072, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 10809724437792986082, "path": 10812277176400524412, "deps": [[1988483478007900009, "unicode_ident", false, 3767155178263335412], [3060637413840920116, "proc_macro2", false, 2791184560887036371], [17990358020177143287, "quote", false, 5742194101450489310]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/syn-7eceb49c6c22082f/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}