{"rustc": 13226066032359371072, "features": "[\"default\"]", "declared_features": "[\"__debug\", \"__docsrs\", \"__inline_const_pat_tests\", \"__only_new_tests\", \"__test\", \"all\", \"assert\", \"assertc\", \"assertcp\", \"const_generics\", \"constant_time_as_str\", \"default\", \"derive\", \"fmt\", \"konst\", \"more_str_macros\", \"nightly_const_generics\", \"rust_1_51\", \"rust_1_64\", \"rust_1_83\"]", "target": 18050621619102943376, "profile": 10809724437792986082, "path": 16453010957818158739, "deps": [[18351378648494636016, "const_format_proc_macros", false, 16749605112926172952]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/const_format-26862e72522e7b96/dep-lib-const_format", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}