{"rustc": 13226066032359371072, "features": "[\"dioxus\"]", "declared_features": "[\"dioxus\"]", "target": 17230149161442505167, "profile": 10809724437792986082, "path": 18290160507093541130, "deps": [[9689903380558560274, "serde", false, 8175013679736651593], [9701365298280180028, "dioxus_core_types", false, 11278087156965753680], [12388982041157692632, "const_serialize", false, 651251979329146005], [14841937903727062428, "dioxus_cli_config", false, 7950954881910594426]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/manganis-core-f71bb0dd4e85b9d3/dep-lib-manganis_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}