{"rustc": 13226066032359371072, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 15664164965450599468, "path": 14887708483491774726, "deps": [[1615478164327904835, "pin_utils", false, 8301600176108974218], [1906322745568073236, "pin_project_lite", false, 3142149170703165179], [5451793922601807560, "slab", false, 4029778355064131472], [7620660491849607393, "futures_core", false, 18028714246556281463], [10565019901765856648, "futures_macro", false, 7081401078395517779], [16240732885093539806, "futures_task", false, 8864766223286077314]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/futures-util-88e93de9d50a7bc9/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}