{"rustc": 13226066032359371072, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3060637413840920116, "build_script_build", false, 13661233871502208426]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/wasm-dev/build/proc-macro2-c812b56ae2a65f28/output", "paths": ["build/probe.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}