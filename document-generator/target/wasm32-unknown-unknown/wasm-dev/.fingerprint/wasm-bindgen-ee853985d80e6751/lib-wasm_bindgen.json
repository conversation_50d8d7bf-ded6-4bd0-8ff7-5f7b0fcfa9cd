{"rustc": 13226066032359371072, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 15001374164661242354, "path": 13079199885640384319, "deps": [[2828590642173593838, "cfg_if", false, 10001439820250838009], [3722963349756955755, "once_cell", false, 7696761793967843389], [6946689283190175495, "build_script_build", false, 14868528597852322370], [7858942147296547339, "rustversion", false, 4990994730493080909], [11382113702854245495, "wasm_bindgen_macro", false, 7433144147283887859]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/wasm-bindgen-ee853985d80e6751/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}