{"rustc": 13226066032359371072, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2415174312781903822, "build_script_build", false, 1264486997890991361]], "local": [{"RerunIfChanged": {"output": "wasm32-unknown-unknown/wasm-dev/build/dioxus-document-cc5acd386ce40205/output", "paths": ["./src/ts/eval.ts", "./src/ts/head.ts"]}}], "rustflags": [], "config": 0, "compile_kind": 0}