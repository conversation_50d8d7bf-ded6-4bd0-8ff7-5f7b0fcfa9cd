{"rustc": 13226066032359371072, "features": "[\"file_engine\", \"mounted\"]", "declared_features": "[\"default\", \"file_engine\", \"hot-reload-context\", \"html-to-rsx\", \"js-sys\", \"mounted\", \"serialize\", \"tokio\"]", "target": 12211965623218830283, "profile": 10809724437792986082, "path": 2417367892291592790, "deps": [[1811549171721445101, "futures_channel", false, 6116106070040652887], [1859894975547053349, "generational_box", false, 18314470907974430578], [2833126123452420110, "dioxus_core", false, 3740970622420549154], [3405920678550729695, "dioxus_core_macro", false, 16561582413425617653], [3490199624569807472, "dioxus_html_internal_macro", false, 16517357367784267252], [7137988838644907815, "dioxus_hooks", false, 8980428815269320040], [7858942147296547339, "rustversion", false, 4990994730493080909], [8606274917505247608, "tracing", false, 15578410458078156891], [9701365298280180028, "dioxus_core_types", false, 11278087156965753680], [10374796949481587011, "enumset", false, 4733153916742954090], [11946729385090170470, "async_trait", false, 1208902035339376836], [14059460239810500800, "euclid", false, 10497216602786018449], [14532484442929614732, "keyboard_types", false, 14829967072804582662]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/wasm-dev/.fingerprint/dioxus-html-b62881580d829287/dep-lib-dioxus_html", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}