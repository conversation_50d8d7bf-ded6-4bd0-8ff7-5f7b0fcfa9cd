{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 13503878198166241647, "path": 13252628174688444908, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 9489445283679095027], [17990358020177143287, "quote", false, 7772496956540595397]], "local": [{"CheckDepInfo": {"dep_info": "wasm-dev/.fingerprint/wasm-bindgen-macro-0a9ef27ba1ab8fee/dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}