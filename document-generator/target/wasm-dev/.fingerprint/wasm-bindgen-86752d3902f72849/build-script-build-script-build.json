{"rustc": 13226066032359371072, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 5408242616063297496, "profile": 6374401459973044251, "path": 1920239301606143114, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "wasm-dev/.fingerprint/wasm-bindgen-86752d3902f72849/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}