{"rustc": 13226066032359371072, "features": "[\"default\", \"minimal_bindings\", \"sledgehammer\", \"webonly\"]", "declared_features": "[\"binary-protocol\", \"default\", \"minimal_bindings\", \"serialize\", \"sledgehammer\", \"webonly\"]", "target": 5408242616063297496, "profile": 15657897354478470176, "path": 17312540593764951454, "deps": [[11439378815190913121, "lazy_js_bundle", false, 13128991992057364723]], "local": [{"CheckDepInfo": {"dep_info": "wasm-dev/.fingerprint/dioxus-interpreter-js-1e5158c64cbfb821/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}