{"rustc": 13226066032359371072, "features": "[\"default\", \"web\"]", "declared_features": "[\"default\", \"web\"]", "target": 12792437031597773705, "profile": 15657897354478470176, "path": 3709380671773665416, "deps": [[4974441333307933176, "syn", false, 6179784350913633815], [17990358020177143287, "quote", false, 7772496956540595397]], "local": [{"CheckDepInfo": {"dep_info": "wasm-dev/.fingerprint/sledgehammer_bindgen_macro-a1d1b5a0abee5d57/dep-lib-sledgehammer_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}