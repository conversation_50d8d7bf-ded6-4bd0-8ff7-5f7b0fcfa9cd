{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 13503878198166241647, "path": 8031608129955885282, "deps": [[3060637413840920116, "proc_macro2", false, 18293720121490802348], [4974441333307933176, "syn", false, 6179784350913633815], [14299170049494554845, "wasm_bindgen_shared", false, 3959628419902720169], [14372503175394433084, "wasm_bindgen_backend", false, 367745975225169796], [17990358020177143287, "quote", false, 7772496956540595397]], "local": [{"CheckDepInfo": {"dep_info": "wasm-dev/.fingerprint/wasm-bindgen-macro-support-66eedbfcbe9e79c2/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}