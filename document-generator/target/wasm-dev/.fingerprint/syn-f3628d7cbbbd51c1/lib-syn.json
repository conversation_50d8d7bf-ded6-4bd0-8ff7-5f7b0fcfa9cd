{"rustc": 13226066032359371072, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 15657897354478470176, "path": 10812277176400524412, "deps": [[1988483478007900009, "unicode_ident", false, 9575023786371560655], [3060637413840920116, "proc_macro2", false, 18293720121490802348], [17990358020177143287, "quote", false, 7772496956540595397]], "local": [{"CheckDepInfo": {"dep_info": "wasm-dev/.fingerprint/syn-f3628d7cbbbd51c1/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}