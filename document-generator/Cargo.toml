[package]
name = "document-generator-ui"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dependencies]
dioxus = { version = "0.6", features = ["web", "router"] }
dioxus-logger = "0.6"
dioxus-web = "0.6"
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
web-sys = { version = "0.3", features = [
  "console",
  "File",
  "FileList",
  "FileReader",
  "Blob",
  "Element",
  "HtmlElement",
  "HtmlInputElement",
  "Event",
  "EventTarget",
  "DragEvent",
  "DataTransfer",
  "DataTransferItem",
  "DataTransferItemList",
] }
js-sys = "0.3"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
serde-wasm-bindgen = "0.6"

chrono = { version = "0.4", features = ["serde", "wasm-bindgen"] }
gloo-utils = "0.2"
gloo-file = "0.3"

[workspace]
members = ["src-tauri"]
