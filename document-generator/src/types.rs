use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct Workspace {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub path: String,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Project {
    pub id: String,
    pub workspace_id: String,
    pub name: String,
    pub description: Option<String>,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DataSource {
    pub id: String,
    pub project_id: String,
    pub name: String,
    pub description: Option<String>,
    pub source_type: DataSourceType,
    pub file_path: String,
    pub headers: Vec<String>,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DataSourceType {
    Csv,
    Excel,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct Template {
    pub id: String,
    pub project_id: String,
    pub name: String,
    pub description: Option<String>,
    pub file_path: String,
    pub placeholders: Vec<Placeholder>,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Placeholder {
    pub name: String,
    pub pattern: String,
    pub data_type: DataType,
    pub position: PlaceholderPosition,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DataType {
    Text,
    Number,
    Date,
    Boolean,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PlaceholderPosition {
    pub paragraph_index: usize,
    pub run_index: usize,
    pub char_start: usize,
    pub char_end: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FieldMapping {
    pub id: String,
    pub project_id: String,
    pub data_source_id: String,
    pub template_id: String,
    pub mapping_rules: HashMap<String, String>,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct GenerationTask {
    pub id: String,
    pub project_id: String,
    pub field_mapping_id: String,
    pub status: TaskStatus,
    pub output_directory: String,
    pub output_filename_pattern: String,
    pub total_records: usize,
    pub processed_records: usize,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParams {
    pub page: Option<usize>,
    pub page_size: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: usize,
    pub page: usize,
    pub page_size: usize,
    pub total_pages: usize,
}

// Tauri command argument types
#[derive(Serialize, Deserialize)]
pub struct CreateWorkspaceArgs {
    pub name: String,
    pub description: Option<String>,
    pub path: String,
}

#[derive(Serialize, Deserialize)]
pub struct CreateProjectArgs {
    pub workspace_id: String,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Serialize, Deserialize)]
pub struct CreateDataSourceArgs {
    pub project_id: String,
    pub name: String,
    pub description: Option<String>,
    pub source_type: DataSourceType,
    pub file_path: String,
}

#[derive(Serialize, Deserialize)]
pub struct CreateTemplateArgs {
    pub project_id: String,
    pub name: String,
    pub description: Option<String>,
    pub file_path: String,
}

#[derive(Serialize, Deserialize)]
pub struct CreateFieldMappingArgs {
    pub project_id: String,
    pub data_source_id: String,
    pub template_id: String,
    pub mapping_rules: HashMap<String, String>,
}

#[derive(Serialize, Deserialize)]
pub struct CreateGenerationTaskArgs {
    pub project_id: String,
    pub field_mapping_id: String,
    pub output_directory: String,
    pub output_filename_pattern: String,
}
