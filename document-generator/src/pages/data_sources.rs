use dioxus::prelude::*;
use crate::components::*;

pub fn DataSourcesPage() -> Element {
    rsx! {
        div { class: "data-sources-page",
            Header {
                title: "数据源管理".to_string(),
                subtitle: Some("管理CSV和Excel数据文件".to_string()),
                children: rsx! {
                    Button {
                        variant: Some("primary".to_string()),
                        rsx! { "添加数据源" }
                    }
                }
            }

            Card {
                div { class: "placeholder-content",
                    h3 { "数据源管理功能开发中..." }
                    p { "此页面将包含数据源的上传、预览和管理功能。" }
                }
            }
        }
    }
}
