use dioxus::prelude::*;
use crate::components::*;

pub fn GenerationPage() -> Element {
    rsx! {
        div { class: "generation-page",
            Header {
                title: "文档生成".to_string(),
                subtitle: Some("批量生成文档并监控进度".to_string()),
                children: rsx! {
                    Button {
                        variant: Some("success".to_string()),
                        "开始生成"
                    }
                }
            }

            Card {
                div { class: "placeholder-content",
                    h3 { "文档生成功能开发中..." }
                    p { "此页面将包含批量生成任务的创建、监控和管理功能。" }
                }
            }
        }
    }
}
