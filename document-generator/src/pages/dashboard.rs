use dioxus::prelude::*;
use crate::components::*;

pub fn Dashboard() -> Element {
    rsx! {
        div { class: "dashboard",
            Header {
                title: "仪表板".to_string(),
                subtitle: Some("文档生成工具概览".to_string()),
            }
            
            div { class: "dashboard-stats",
                StatsCard {
                    title: "工作空间".to_string(),
                    value: "3".to_string(),
                    icon: "📁".to_string(),
                    color: Some("blue".to_string()),
                }
                StatsCard {
                    title: "项目".to_string(),
                    value: "12".to_string(),
                    icon: "📊".to_string(),
                    color: Some("green".to_string()),
                }
                StatsCard {
                    title: "模板".to_string(),
                    value: "8".to_string(),
                    icon: "📝".to_string(),
                    color: Some("purple".to_string()),
                }
                StatsCard {
                    title: "生成任务".to_string(),
                    value: "25".to_string(),
                    icon: "⚡".to_string(),
                    color: Some("orange".to_string()),
                }
            }

            div { class: "dashboard-content",
                div { class: "dashboard-grid",
                    Card {
                        title: Some("最近项目".to_string()),
                        div { class: "recent-projects",
                            div { class: "project-item",
                                div { class: "project-info",
                                    h4 { "员工信息管理" }
                                    p { "更新于 2小时前" }
                                }
                                span { class: "project-status active", "活跃" }
                            }
                            div { class: "project-item",
                                div { class: "project-info",
                                    h4 { "合同文档生成" }
                                    p { "更新于 1天前" }
                                }
                                span { class: "project-status", "完成" }
                            }
                            div { class: "project-item",
                                div { class: "project-info",
                                    h4 { "报告模板系统" }
                                    p { "更新于 3天前" }
                                }
                                span { class: "project-status", "进行中" }
                            }
                        }
                    }

                    Card {
                        title: Some("快速操作".to_string()),
                        div { class: "quick-actions",
                            Button {
                                variant: Some("primary".to_string()),
                                "创建新项目"
                            }
                            Button {
                                variant: Some("secondary".to_string()),
                                "导入数据源"
                            }
                            Button {
                                variant: Some("secondary".to_string()),
                                "上传模板"
                            }
                            Button {
                                variant: Some("success".to_string()),
                                "开始生成"
                            }
                        }
                    }
                }

                Card {
                    title: Some("系统状态".to_string()),
                    rsx! {
                        div { class: "system-status",
                            div { class: "status-item",
                                span { class: "status-indicator success" }
                                span { "数据库连接正常" }
                            }
                            div { class: "status-item",
                                span { class: "status-indicator success" }
                                span { "文件系统可用" }
                            }
                            div { class: "status-item",
                                span { class: "status-indicator warning" }
                                span { "磁盘空间: 75% 已使用" }
                            }
                        }
                    }
                }
            }
        }
    }
}
