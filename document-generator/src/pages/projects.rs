use dioxus::prelude::*;
use crate::components::*;
use crate::types::*;
use wasm_bindgen::prelude::*;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

pub fn ProjectsPage() -> Element {
    let mut projects = use_signal(|| Vec::<Project>::new());
    let mut workspaces = use_signal(|| Vec::<Workspace>::new());
    let mut show_create_modal = use_signal(|| false);
    let mut new_project_name = use_signal(|| String::new());
    let mut new_project_description = use_signal(|| String::new());
    let mut selected_workspace = use_signal(|| String::new());

    // Load data on component mount
    use_effect(move || {
        spawn(async move {
            // Load workspaces
            let workspaces_result = invoke("list_workspaces", JsValue::NULL).await;
            if let Ok(workspaces_data) = serde_wasm_bindgen::from_value::<ApiResponse<Vec<Workspace>>>(workspaces_result) {
                if let Some(ws_list) = workspaces_data.data {
                    workspaces.set(ws_list);
                }
            }

            // Load projects
            let projects_result = invoke("list_projects", JsValue::NULL).await;
            if let Ok(projects_data) = serde_wasm_bindgen::from_value::<ApiResponse<Vec<Project>>>(projects_result) {
                if let Some(proj_list) = projects_data.data {
                    projects.set(proj_list);
                }
            }
        });
    });

    let create_project = move |_| {
        let workspace_id = selected_workspace.read().clone();
        let name = new_project_name.read().clone();
        let description = if new_project_description.read().is_empty() {
            None
        } else {
            Some(new_project_description.read().clone())
        };

        if workspace_id.is_empty() || name.is_empty() {
            return;
        }

        spawn(async move {
            let args = CreateProjectArgs {
                workspace_id,
                name,
                description,
            };
            
            let args_value = serde_wasm_bindgen::to_value(&args).unwrap();
            let result = invoke("create_project", args_value).await;
            
            if let Ok(response) = serde_wasm_bindgen::from_value::<ApiResponse<Project>>(result) {
                if response.success {
                    // Reload projects
                    let projects_result = invoke("list_projects", JsValue::NULL).await;
                    if let Ok(projects_data) = serde_wasm_bindgen::from_value::<ApiResponse<Vec<Project>>>(projects_result) {
                        if let Some(proj_list) = projects_data.data {
                            projects.set(proj_list);
                        }
                    }
                    
                    // Reset form and close modal
                    new_project_name.set(String::new());
                    new_project_description.set(String::new());
                    selected_workspace.set(String::new());
                    show_create_modal.set(false);
                }
            }
        });
    };

    rsx! {
        div { class: "projects-page",
            Header {
                title: "项目管理".to_string(),
                subtitle: Some("管理您的文档生成项目".to_string()),
                children: Some(rsx! {
                    Button {
                        variant: Some("primary".to_string()),
                        onclick: move |_| show_create_modal.set(true),
                        "创建项目"
                    }
                })
            }

            Card {
                Table {
                        headers: vec![
                            "项目名称".to_string(),
                            "工作空间".to_string(),
                            "描述".to_string(),
                            "创建时间".to_string(),
                            "操作".to_string(),
                        ],
                        for project in projects.read().iter() {
                            TableRow {
                                TableCell { "{project.name}" }
                                TableCell {
                                    {workspaces.read().iter()
                                        .find(|w| w.id == project.workspace_id)
                                        .map(|w| w.name.clone())
                                        .unwrap_or_else(|| "未知".to_string())}
                                }
                                TableCell {
                                    {project.description.as_ref().unwrap_or(&"无描述".to_string())}
                                }
                                TableCell { "{project.created_at}" }
                                TableCell {
                                    div { class: "action-buttons",
                                        Button {
                                            variant: Some("secondary".to_string()),
                                            size: Some("small".to_string()),
                                            "编辑"
                                        }
                                        Button {
                                            variant: Some("danger".to_string()),
                                            size: Some("small".to_string()),
                                            "删除"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Modal {
                title: "创建新项目".to_string(),
                is_open: show_create_modal.read().clone(),
                on_close: move |_| show_create_modal.set(false),
                    form {
                        onsubmit: create_project,
                        Select {
                            label: Some("选择工作空间".to_string()),
                            value: selected_workspace.read().clone(),
                            onchange: move |evt| selected_workspace.set(evt.value()),
                            options: workspaces.read().iter().map(|w| (w.id.clone(), w.name.clone())).collect(),
                            required: Some(true),
                        }
                        
                        Input {
                            label: Some("项目名称".to_string()),
                            placeholder: Some("输入项目名称".to_string()),
                            value: new_project_name.read().clone(),
                            oninput: move |evt| new_project_name.set(evt.value()),
                            required: Some(true),
                        }
                        
                        Textarea {
                            label: Some("项目描述".to_string()),
                            placeholder: Some("输入项目描述（可选）".to_string()),
                            value: new_project_description.read().clone(),
                            oninput: move |evt| new_project_description.set(evt.value()),
                        }
                        
                        div { class: "modal-actions",
                            Button {
                                variant: Some("secondary".to_string()),
                                onclick: move |_| show_create_modal.set(false),
                                "取消"
                            }
                            Button {
                                variant: Some("primary".to_string()),
                                r#type: Some("submit".to_string()),
                                "创建"
                            }
                        }
                    }
            }
        }
    }
}
