use dioxus::prelude::*;
use crate::components::*;

pub fn FieldMappingPage() -> Element {
    rsx! {
        div { class: "field-mapping-page",
            Header {
                title: "字段映射".to_string(),
                subtitle: Some("配置数据字段与模板占位符的映射关系".to_string()),
                children: rsx! {
                    Button {
                        variant: Some("primary".to_string()),
                        rsx! { "创建映射" }
                    }
                }
            }

            Card {
                div { class: "placeholder-content",
                    h3 { "字段映射功能开发中..." }
                    p { "此页面将包含拖拽式字段映射界面。" }
                }
            }
        }
    }
}
