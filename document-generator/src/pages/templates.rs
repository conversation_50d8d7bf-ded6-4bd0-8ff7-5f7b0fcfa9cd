use dioxus::prelude::*;
use crate::components::*;

pub fn TemplatesPage() -> Element {
    rsx! {
        div { class: "templates-page",
            Header {
                title: "模板管理".to_string(),
                subtitle: Some("管理Word文档模板".to_string()),
                children: rsx! {
                    Button {
                        variant: Some("primary".to_string()),
                        rsx! { "上传模板" }
                    }
                }
            }

            Card {
                div { class: "placeholder-content",
                    h3 { "模板管理功能开发中..." }
                    p { "此页面将包含模板的上传、预览和占位符识别功能。" }
                }
            }
        }
    }
}
