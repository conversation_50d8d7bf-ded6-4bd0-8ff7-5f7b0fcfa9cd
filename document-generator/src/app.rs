#![allow(non_snake_case)]

use dioxus::prelude::*;
use serde::{Deserialize, Serialize};
use wasm_bindgen::prelude::*;

use crate::components::*;
use crate::pages::*;
use crate::types::*;

#[wasm_bindgen]
extern "C" {
    #[wasm_bindgen(js_namespace = ["window", "__TAURI__", "core"])]
    async fn invoke(cmd: &str, args: JsValue) -> JsValue;
}

#[derive(Clone, Copy, PartialEq)]
pub enum AppPage {
    Dashboard,
    Projects,
    DataSources,
    Templates,
    FieldMapping,
    Generation,
}

pub fn App() -> Element {
    let mut current_page = use_signal(|| AppPage::Dashboard);

    rsx! {
        link { rel: "stylesheet", href: "styles.css" }
        div { class: "app",
            Sidebar {
                current_page: current_page.read().clone(),
                on_page_change: move |page| current_page.set(page)
            }
            main { class: "main-content",
                match current_page.read().clone() {
                    AppPage::Dashboard => rsx! { Dashboard {} },
                    AppPage::Projects => rsx! { ProjectsPage {} },
                    AppPage::DataSources => rsx! { DataSourcesPage {} },
                    AppPage::Templates => rsx! { TemplatesPage {} },
                    AppPage::FieldMapping => rsx! { FieldMappingPage {} },
                    AppPage::Generation => rsx! { GenerationPage {} },
                }
            }
        }
    }
}