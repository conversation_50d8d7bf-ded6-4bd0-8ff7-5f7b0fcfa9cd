use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct ModalProps {
    pub title: String,
    pub is_open: bool,
    pub on_close: EventHandler<()>,
    pub children: Element,
}

pub fn Modal(props: ModalProps) -> Element {
    if !props.is_open {
        return rsx! { div {} };
    }

    rsx! {
        div { class: "modal-overlay",
            onclick: move |_| props.on_close.call(()),
            div { class: "modal",
                onclick: move |evt| evt.stop_propagation(),
                div { class: "modal-header",
                    h3 { class: "modal-title", "{props.title}" }
                    button {
                        class: "modal-close",
                        onclick: move |_| props.on_close.call(()),
                        "×"
                    }
                }
                div { class: "modal-content",
                    {props.children}
                }
            }
        }
    }
}
