use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct CardProps {
    pub title: Option<String>,
    pub children: Element,
    pub class: Option<String>,
}

pub fn Card(props: CardProps) -> Element {
    let card_class = format!("card {}", props.class.unwrap_or_default());
    
    rsx! {
        div { class: "{card_class}",
            if let Some(title) = props.title {
                div { class: "card-header",
                    h3 { class: "card-title", "{title}" }
                }
            }
            div { class: "card-content",
                {props.children}
            }
        }
    }
}

#[derive(Props, Clone, PartialEq)]
pub struct StatsCardProps {
    pub title: String,
    pub value: String,
    pub icon: String,
    pub color: Option<String>,
}

pub fn StatsCard(props: StatsCardProps) -> Element {
    let color_class = props.color.unwrap_or_else(|| "blue".to_string());
    
    rsx! {
        div { class: "stats-card {color_class}",
            div { class: "stats-icon", "{props.icon}" }
            div { class: "stats-content",
                div { class: "stats-value", "{props.value}" }
                div { class: "stats-title", "{props.title}" }
            }
        }
    }
}
