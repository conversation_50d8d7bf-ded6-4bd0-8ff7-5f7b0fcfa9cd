use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct ButtonProps {
    pub children: Element,
    pub onclick: Option<EventHandler<MouseEvent>>,
    pub variant: Option<String>,
    pub size: Option<String>,
    pub disabled: Option<bool>,
    pub r#type: Option<String>,
}

pub fn Button(props: ButtonProps) -> Element {
    let variant = props.variant.unwrap_or_else(|| "primary".to_string());
    let size = props.size.unwrap_or_else(|| "medium".to_string());
    let disabled = props.disabled.unwrap_or(false);
    let button_type = props.r#type.unwrap_or_else(|| "button".to_string());
    
    let class = format!("btn btn-{} btn-{}", variant, size);
    
    rsx! {
        button {
            class: "{class}",
            r#type: "{button_type}",
            disabled: disabled,
            onclick: move |evt| {
                if let Some(handler) = &props.onclick {
                    handler.call(evt);
                }
            },
            {props.children}
        }
    }
}
