use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct HeaderProps {
    pub title: String,
    pub subtitle: Option<String>,
    pub children: Option<Element>,
}

pub fn Header(props: HeaderProps) -> Element {
    rsx! {
        header { class: "page-header",
            div { class: "header-content",
                div { class: "header-text",
                    h1 { class: "header-title", "{props.title}" }
                    if let Some(subtitle) = props.subtitle {
                        p { class: "header-subtitle", "{subtitle}" }
                    }
                }
                if let Some(children) = props.children {
                    div { class: "header-actions",
                        {children}
                    }
                }
            }
        }
    }
}
