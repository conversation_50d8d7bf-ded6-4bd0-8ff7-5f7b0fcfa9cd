use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct TableProps {
    pub headers: Vec<String>,
    pub children: Element,
}

pub fn Table(props: TableProps) -> Element {
    rsx! {
        div { class: "table-container",
            table { class: "table",
                thead {
                    tr {
                        for header in props.headers {
                            th { "{header}" }
                        }
                    }
                }
                tbody {
                    {props.children}
                }
            }
        }
    }
}

#[derive(<PERSON><PERSON>, <PERSON>lone, PartialEq)]
pub struct TableRowProps {
    pub children: Element,
    pub onclick: Option<EventHandler<MouseEvent>>,
}

pub fn TableRow(props: TableRowProps) -> Element {
    rsx! {
        tr {
            class: if props.onclick.is_some() { "table-row clickable" } else { "table-row" },
            onclick: move |evt| {
                if let Some(handler) = &props.onclick {
                    handler.call(evt);
                }
            },
            {props.children}
        }
    }
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct TableCellProps {
    pub children: Element,
}

pub fn TableCell(props: TableCellProps) -> Element {
    rsx! {
        td { class: "table-cell",
            {props.children}
        }
    }
}
