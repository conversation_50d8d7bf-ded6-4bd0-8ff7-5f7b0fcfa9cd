use dioxus::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
pub struct InputProps {
    pub label: Option<String>,
    pub placeholder: Option<String>,
    pub value: String,
    pub oninput: EventHandler<FormEvent>,
    pub r#type: Option<String>,
    pub required: Option<bool>,
    pub disabled: Option<bool>,
}

pub fn Input(props: InputProps) -> Element {
    let input_type = props.r#type.unwrap_or_else(|| "text".to_string());
    let required = props.required.unwrap_or(false);
    let disabled = props.disabled.unwrap_or(false);
    
    rsx! {
        div { class: "input-group",
            if let Some(label) = props.label {
                label { class: "input-label", "{label}" }
            }
            input {
                class: "input",
                r#type: "{input_type}",
                placeholder: props.placeholder.unwrap_or_default(),
                value: "{props.value}",
                required: required,
                disabled: disabled,
                oninput: move |evt| props.oninput.call(evt)
            }
        }
    }
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Eq)]
pub struct TextareaProps {
    pub label: Option<String>,
    pub placeholder: Option<String>,
    pub value: String,
    pub oninput: EventHandler<FormEvent>,
    pub rows: Option<u32>,
    pub required: Option<bool>,
    pub disabled: Option<bool>,
}

pub fn Textarea(props: TextareaProps) -> Element {
    let rows = props.rows.unwrap_or(3);
    let required = props.required.unwrap_or(false);
    let disabled = props.disabled.unwrap_or(false);
    
    rsx! {
        div { class: "input-group",
            if let Some(label) = props.label {
                label { class: "input-label", "{label}" }
            }
            textarea {
                class: "textarea",
                placeholder: props.placeholder.unwrap_or_default(),
                value: "{props.value}",
                rows: "{rows}",
                required: required,
                disabled: disabled,
                oninput: move |evt| props.oninput.call(evt)
            }
        }
    }
}

#[derive(Props, Clone, PartialEq)]
pub struct SelectProps {
    pub label: Option<String>,
    pub value: String,
    pub onchange: EventHandler<FormEvent>,
    pub options: Vec<(String, String)>, // (value, label)
    pub required: Option<bool>,
    pub disabled: Option<bool>,
}

pub fn Select(props: SelectProps) -> Element {
    let required = props.required.unwrap_or(false);
    let disabled = props.disabled.unwrap_or(false);
    
    rsx! {
        div { class: "input-group",
            if let Some(label) = props.label {
                label { class: "input-label", "{label}" }
            }
            select {
                class: "select",
                value: "{props.value}",
                required: required,
                disabled: disabled,
                onchange: move |evt| props.onchange.call(evt),
                for (value, label) in props.options {
                    option { value: "{value}", "{label}" }
                }
            }
        }
    }
}
