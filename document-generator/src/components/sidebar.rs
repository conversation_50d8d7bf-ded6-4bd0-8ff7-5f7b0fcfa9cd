use dioxus::prelude::*;
use crate::app::AppPage;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct SidebarProps {
    pub current_page: AppPage,
    pub on_page_change: EventHandler<AppPage>,
}

pub fn Sidebar(props: SidebarProps) -> Element {
    let menu_items = vec![
        (AppPage::Dashboard, "仪表板", "📊"),
        (AppPage::Projects, "项目管理", "📁"),
        (AppPage::DataSources, "数据源", "📄"),
        (AppPage::Templates, "模板管理", "📝"),
        (AppPage::FieldMapping, "字段映射", "🔗"),
        (AppPage::Generation, "文档生成", "⚡"),
    ];

    rsx! {
        aside { class: "sidebar",
            div { class: "sidebar-header",
                h2 { "文档生成器" }
            }
            nav { class: "sidebar-nav",
                ul {
                    for (page, label, icon) in menu_items {
                        li {
                            button {
                                class: if props.current_page == page { "nav-item active" } else { "nav-item" },
                                onclick: move |_| props.on_page_change.call(page),
                                span { class: "nav-icon", "{icon}" }
                                span { class: "nav-label", "{label}" }
                            }
                        }
                    }
                }
            }
        }
    }
}
