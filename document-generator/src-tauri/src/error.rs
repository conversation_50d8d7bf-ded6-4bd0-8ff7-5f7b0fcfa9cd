use serde::{Deserialize, Serialize};
use thiserror::Error;

/// 应用程序错误类型
#[derive(Erro<PERSON>, Debug, Serialize, Deserialize)]
pub enum AppError {
    #[error("IO错误: {0}")]
    Io(String),

    #[error("JSON序列化/反序列化错误: {0}")]
    <PERSON><PERSON>(String),

    #[error("CSV处理错误: {0}")]
    Csv(String),

    #[error("Excel处理错误: {0}")]
    Excel(String),

    #[error("文档处理错误: {0}")]
    Document(String),

    #[error("编码错误: {0}")]
    Encoding(String),

    #[error("文件不存在: {0}")]
    FileNotFound(String),

    #[error("文件格式不支持: {0}")]
    UnsupportedFormat(String),

    #[error("资源不存在: {0}")]
    NotFound(String),

    #[error("数据验证错误: {0}")]
    Validation(String),

    #[error("数据库错误: {0}")]
    Database(String),

    #[error("配置错误: {0}")]
    Config(String),

    #[error("任务执行错误: {0}")]
    Task(String),

    #[error("权限错误: {0}")]
    Permission(String),

    #[error("网络错误: {0}")]
    Network(String),

    #[error("未知错误: {0}")]
    Unknown(String),
}

impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        AppError::Io(err.to_string())
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::Json(err.to_string())
    }
}

impl From<csv::Error> for AppError {
    fn from(err: csv::Error) -> Self {
        AppError::Csv(err.to_string())
    }
}

impl From<calamine::Error> for AppError {
    fn from(err: calamine::Error) -> Self {
        AppError::Excel(err.to_string())
    }
}

impl From<zip::result::ZipError> for AppError {
    fn from(err: zip::result::ZipError) -> Self {
        AppError::Document(format!("ZIP错误: {}", err))
    }
}

/// 应用程序结果类型
pub type AppResult<T> = Result<T, AppError>;

/// 错误响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub code: String,
    pub details: Option<String>,
}

impl From<AppError> for ErrorResponse {
    fn from(err: AppError) -> Self {
        let code = match &err {
            AppError::Io(_) => "IO_ERROR",
            AppError::Json(_) => "JSON_ERROR",
            AppError::Csv(_) => "CSV_ERROR",
            AppError::Excel(_) => "EXCEL_ERROR",
            AppError::Document(_) => "DOCUMENT_ERROR",
            AppError::Encoding(_) => "ENCODING_ERROR",
            AppError::FileNotFound(_) => "FILE_NOT_FOUND",
            AppError::UnsupportedFormat(_) => "UNSUPPORTED_FORMAT",
            AppError::Validation(_) => "VALIDATION_ERROR",
            AppError::Database(_) => "DATABASE_ERROR",
            AppError::Config(_) => "CONFIG_ERROR",
            AppError::Task(_) => "TASK_ERROR",
            AppError::Permission(_) => "PERMISSION_ERROR",
            AppError::Network(_) => "NETWORK_ERROR",
            AppError::Unknown(_) => "UNKNOWN_ERROR",
        };

        Self {
            error: err.to_string(),
            code: code.to_string(),
            details: None,
        }
    }
}

/// 验证错误详情
#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationError {
    pub field: String,
    pub message: String,
}

/// 批量验证错误
#[derive(Debug, Serialize, Deserialize)]
pub struct ValidationErrors {
    pub errors: Vec<ValidationError>,
}

impl ValidationErrors {
    pub fn new() -> Self {
        Self {
            errors: Vec::new(),
        }
    }

    pub fn add(&mut self, field: String, message: String) {
        self.errors.push(ValidationError { field, message });
    }

    pub fn is_empty(&self) -> bool {
        self.errors.is_empty()
    }

    pub fn into_app_error(self) -> AppError {
        let messages: Vec<String> = self.errors
            .into_iter()
            .map(|e| format!("{}: {}", e.field, e.message))
            .collect();
        AppError::Validation(messages.join("; "))
    }
}

impl Default for ValidationErrors {
    fn default() -> Self {
        Self::new()
    }
}
