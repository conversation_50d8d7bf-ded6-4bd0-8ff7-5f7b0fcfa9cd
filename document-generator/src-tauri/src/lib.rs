mod models;
mod error;
mod config;
mod commands;
mod services;

use crate::config::{AppConfig, DataStore};
use std::sync::Mutex;

/// 应用程序状态
pub struct AppState {
    pub data_store: Mutex<DataStore>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 加载配置
    let config = AppConfig::load().expect("Failed to load configuration");
    let mut data_store = DataStore::new(config);

    // 加载所有数据
    if let Err(e) = data_store.load_all() {
        eprintln!("Warning: Failed to load some data: {}", e);
    }

    let app_state = AppState {
        data_store: Mutex::new(data_store),
    };

    tauri::Builder::default()
        .manage(app_state)
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            commands::workspace::get_workspaces,
            commands::workspace::create_workspace,
            commands::workspace::update_workspace,
            commands::workspace::delete_workspace,
            commands::project::get_projects,
            commands::project::create_project,
            commands::project::update_project,
            commands::project::delete_project,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
