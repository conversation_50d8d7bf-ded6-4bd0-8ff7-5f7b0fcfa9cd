mod models;
mod error;
mod config;
mod commands;
mod services;

use crate::config::{AppConfig, DataStore};
use tokio::sync::Mutex;

/// 应用程序状态
#[derive(Clone)]
pub struct AppState {
    pub data_store: Mutex<DataStore>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 加载配置
    let config = AppConfig::load().expect("Failed to load configuration");
    let mut data_store = DataStore::new(config);

    // 加载所有数据
    if let Err(e) = data_store.load_all() {
        eprintln!("Warning: Failed to load some data: {}", e);
    }

    let app_state = AppState {
        data_store: Mutex::new(data_store),
    };

    tauri::Builder::default()
        .manage(app_state)
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            // 工作区管理
            commands::workspace::get_workspaces,
            commands::workspace::create_workspace,
            commands::workspace::update_workspace,
            commands::workspace::delete_workspace,
            // 项目管理
            commands::project::get_projects,
            commands::project::create_project,
            commands::project::update_project,
            commands::project::delete_project,
            // 数据源管理
            commands::data_source::get_data_sources,
            commands::data_source::create_data_source,
            commands::data_source::update_data_source,
            commands::data_source::delete_data_source,
            commands::data_source::analyze_data_source,
            // 模板管理
            commands::template::get_templates,
            commands::template::create_template,
            commands::template::update_template,
            commands::template::delete_template,
            commands::template::analyze_template,
            // 字段映射管理
            commands::field_mapping::get_field_mappings,
            commands::field_mapping::create_field_mapping,
            commands::field_mapping::update_field_mapping,
            commands::field_mapping::delete_field_mapping,
            commands::field_mapping::add_mapping_rule,
            commands::field_mapping::remove_mapping_rule,
            commands::field_mapping::update_mapping_rules,
            // 文档生成
            commands::generation::get_generation_tasks,
            commands::generation::create_generation_task,
            commands::generation::update_generation_task,
            commands::generation::delete_generation_task,
            commands::generation::start_generation_task,
            commands::generation::cancel_generation_task,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
