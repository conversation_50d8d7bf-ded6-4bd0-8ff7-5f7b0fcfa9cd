use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// 工作空间配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Workspace {
    pub id: Uuid,
    pub name: String,
    pub path: String,
    pub projects: Vec<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 项目配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub workspace_id: Uuid,
    pub data_sources: Vec<Uuid>,
    pub templates: Vec<Uuid>,
    pub field_mappings: Vec<Uuid>,
    pub generation_tasks: Vec<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 数据源类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum DataSourceType {
    Csv,
    Excel,
}

/// 数据源配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSource {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub file_path: PathBuf,
    pub source_type: DataSourceType,
    pub encoding: Option<String>,
    pub has_header: bool,
    pub delimiter: Option<char>, // CSV专用
    pub sheet_name: Option<String>, // Excel专用
    pub columns: Vec<DataColumn>,
    pub headers: Vec<String>, // 添加headers字段
    pub row_count: usize,
    pub project_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 数据列信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataColumn {
    pub index: usize,
    pub name: String,
    pub data_type: DataType,
    pub sample_values: Vec<String>,
}

/// 数据类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataType {
    Text,
    Number,
    Date,
    Boolean,
}

/// 模板配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Template {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub file_path: PathBuf,
    pub placeholders: Vec<Placeholder>,
    pub project_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 占位符信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Placeholder {
    pub name: String,
    pub pattern: String, // 原始占位符模式，如 {name}, {{name}}, ${name}
    pub position: PlaceholderPosition,
    pub data_type: DataType,
}

/// 占位符位置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlaceholderPosition {
    pub paragraph_index: usize,
    pub run_index: usize,
    pub char_start: usize,
    pub char_end: usize,
}

/// 映射规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MappingRule {
    pub source_field: String,
    pub target_placeholder: String,
    pub default_value: Option<String>,
}

/// 字段映射配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldMapping {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub data_source_id: Uuid,
    pub template_id: Uuid,
    pub mapping_rules: Vec<MappingRule>,
    pub mappings: HashMap<String, String>, // placeholder_name -> column_name (保留向后兼容)
    pub project_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 生成任务状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
}

/// 输出格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputFormat {
    Docx,
    Pdf,
}

/// 文档生成任务
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationTask {
    pub id: Uuid,
    pub name: String,
    pub field_mapping_id: Uuid,
    pub output_format: OutputFormat,
    pub output_directory: String,
    pub filename_pattern: String, // 支持占位符，如 "document_{name}_{date}"
    pub status: TaskStatus,
    pub progress: f32, // 0.0 - 1.0
    pub total_documents: usize,
    pub completed_documents: usize,
    pub failed_documents: usize,
    pub error_message: Option<String>,
    pub project_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 生成结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationResult {
    pub task_id: Uuid,
    pub row_index: usize,
    pub output_file: String,
    pub success: bool,
    pub error_message: Option<String>,
    pub generated_at: DateTime<Utc>,
}

/// API响应包装器
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
        }
    }
}

/// 分页查询参数
#[derive(Debug, Deserialize)]
pub struct PaginationParams {
    pub page: Option<usize>,
    pub page_size: Option<usize>,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: Some(1),
            page_size: Some(20),
        }
    }
}

/// 分页响应
#[derive(Debug, Serialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: usize,
    pub page: usize,
    pub page_size: usize,
    pub total_pages: usize,
}

impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, total: usize, page: usize, page_size: usize) -> Self {
        let total_pages = (total + page_size - 1) / page_size;
        Self {
            items,
            total,
            page,
            page_size,
            total_pages,
        }
    }
}
