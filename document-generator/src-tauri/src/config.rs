use crate::error::{AppError, AppResult};
use crate::models::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use uuid::Uuid;

/// 应用程序配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub app_data_dir: PathBuf,
    pub workspaces_dir: PathBuf,
    pub temp_dir: PathBuf,
    pub max_file_size: usize, // 字节
    pub supported_encodings: Vec<String>,
    pub default_encoding: String,
    pub csv_delimiters: Vec<char>,
    pub default_csv_delimiter: char,
    pub max_preview_rows: usize,
    pub max_concurrent_tasks: usize,
}

impl Default for AppConfig {
    fn default() -> Self {
        let app_data_dir = dirs::data_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join("DocumentGenerator");

        Self {
            workspaces_dir: app_data_dir.join("workspaces"),
            temp_dir: app_data_dir.join("temp"),
            app_data_dir,
            max_file_size: 100 * 1024 * 1024, // 100MB
            supported_encodings: vec![
                "UTF-8".to_string(),
                "GBK".to_string(),
                "GB2312".to_string(),
                "UTF-16".to_string(),
                "ISO-8859-1".to_string(),
            ],
            default_encoding: "UTF-8".to_string(),
            csv_delimiters: vec![',', ';', '\t', '|'],
            default_csv_delimiter: ',',
            max_preview_rows: 100,
            max_concurrent_tasks: 4,
        }
    }
}

impl AppConfig {
    /// 加载配置文件
    pub fn load() -> AppResult<Self> {
        let config_path = Self::config_file_path();
        
        if config_path.exists() {
            let content = fs::read_to_string(&config_path)?;
            let config: AppConfig = serde_json::from_str(&content)?;
            config.ensure_directories()?;
            Ok(config)
        } else {
            let config = Self::default();
            config.save()?;
            Ok(config)
        }
    }

    /// 保存配置文件
    pub fn save(&self) -> AppResult<()> {
        self.ensure_directories()?;
        let config_path = Self::config_file_path();
        let content = serde_json::to_string_pretty(self)?;
        fs::write(config_path, content)?;
        Ok(())
    }

    /// 获取配置文件路径
    fn config_file_path() -> PathBuf {
        dirs::config_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join("DocumentGenerator")
            .join("config.json")
    }

    /// 确保必要的目录存在
    fn ensure_directories(&self) -> AppResult<()> {
        fs::create_dir_all(&self.app_data_dir)?;
        fs::create_dir_all(&self.workspaces_dir)?;
        fs::create_dir_all(&self.temp_dir)?;
        
        // 确保配置目录存在
        if let Some(parent) = Self::config_file_path().parent() {
            fs::create_dir_all(parent)?;
        }
        
        Ok(())
    }

    /// 获取工作空间目录
    pub fn workspace_dir(&self, workspace_id: &Uuid) -> PathBuf {
        self.workspaces_dir.join(workspace_id.to_string())
    }

    /// 获取项目目录
    pub fn project_dir(&self, workspace_id: &Uuid, project_id: &Uuid) -> PathBuf {
        self.workspace_dir(workspace_id).join(project_id.to_string())
    }

    /// 获取数据源目录
    pub fn data_sources_dir(&self, workspace_id: &Uuid, project_id: &Uuid) -> PathBuf {
        self.project_dir(workspace_id, project_id).join("data_sources")
    }

    /// 获取模板目录
    pub fn templates_dir(&self, workspace_id: &Uuid, project_id: &Uuid) -> PathBuf {
        self.project_dir(workspace_id, project_id).join("templates")
    }

    /// 获取输出目录
    pub fn output_dir(&self, workspace_id: &Uuid, project_id: &Uuid) -> PathBuf {
        self.project_dir(workspace_id, project_id).join("output")
    }

    /// 获取临时文件路径
    pub fn temp_file_path(&self, filename: &str) -> PathBuf {
        self.temp_dir.join(filename)
    }
}

/// 数据存储管理器
pub struct DataStore {
    config: AppConfig,
    pub workspaces: HashMap<Uuid, Workspace>,
    pub projects: HashMap<Uuid, Project>,
    pub data_sources: HashMap<Uuid, DataSource>,
    pub templates: HashMap<Uuid, Template>,
    pub field_mappings: HashMap<Uuid, FieldMapping>,
    pub generation_tasks: HashMap<Uuid, GenerationTask>,
}

impl DataStore {
    /// 创建新的数据存储管理器
    pub fn new(config: AppConfig) -> Self {
        Self {
            config,
            workspaces: HashMap::new(),
            projects: HashMap::new(),
            data_sources: HashMap::new(),
            templates: HashMap::new(),
            field_mappings: HashMap::new(),
            generation_tasks: HashMap::new(),
        }
    }

    /// 加载所有数据
    pub fn load_all(&mut self) -> AppResult<()> {
        self.load_workspaces()?;
        self.load_projects()?;
        self.load_data_sources()?;
        self.load_templates()?;
        self.load_field_mappings()?;
        self.load_generation_tasks()?;
        Ok(())
    }

    /// 加载工作空间
    fn load_workspaces(&mut self) -> AppResult<()> {
        let workspaces_dir = &self.config.workspaces_dir;
        if !workspaces_dir.exists() {
            return Ok(());
        }

        for entry in fs::read_dir(workspaces_dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_dir() {
                let workspace_file = path.join("workspace.json");
                if workspace_file.exists() {
                    let content = fs::read_to_string(workspace_file)?;
                    let workspace: Workspace = serde_json::from_str(&content)?;
                    self.workspaces.insert(workspace.id, workspace);
                }
            }
        }
        Ok(())
    }

    /// 加载项目
    fn load_projects(&mut self) -> AppResult<()> {
        for workspace in self.workspaces.values() {
            let workspace_dir = self.config.workspace_dir(&workspace.id);
            for project_id in &workspace.projects {
                let project_file = workspace_dir.join(project_id.to_string()).join("project.json");
                if project_file.exists() {
                    let content = fs::read_to_string(project_file)?;
                    let project: Project = serde_json::from_str(&content)?;
                    self.projects.insert(project.id, project);
                }
            }
        }
        Ok(())
    }

    /// 加载数据源
    fn load_data_sources(&mut self) -> AppResult<()> {
        for project in self.projects.values() {
            let project_dir = self.config.project_dir(&project.workspace_id, &project.id);
            for data_source_id in &project.data_sources {
                let data_source_file = project_dir.join("data_sources").join(format!("{}.json", data_source_id));
                if data_source_file.exists() {
                    let content = fs::read_to_string(data_source_file)?;
                    let data_source: DataSource = serde_json::from_str(&content)?;
                    self.data_sources.insert(data_source.id, data_source);
                }
            }
        }
        Ok(())
    }

    /// 加载模板
    fn load_templates(&mut self) -> AppResult<()> {
        for project in self.projects.values() {
            let project_dir = self.config.project_dir(&project.workspace_id, &project.id);
            for template_id in &project.templates {
                let template_file = project_dir.join("templates").join(format!("{}.json", template_id));
                if template_file.exists() {
                    let content = fs::read_to_string(template_file)?;
                    let template: Template = serde_json::from_str(&content)?;
                    self.templates.insert(template.id, template);
                }
            }
        }
        Ok(())
    }

    /// 加载字段映射
    fn load_field_mappings(&mut self) -> AppResult<()> {
        for project in self.projects.values() {
            let project_dir = self.config.project_dir(&project.workspace_id, &project.id);
            for mapping_id in &project.field_mappings {
                let mapping_file = project_dir.join("field_mappings").join(format!("{}.json", mapping_id));
                if mapping_file.exists() {
                    let content = fs::read_to_string(mapping_file)?;
                    let mapping: FieldMapping = serde_json::from_str(&content)?;
                    self.field_mappings.insert(mapping.id, mapping);
                }
            }
        }
        Ok(())
    }

    /// 加载生成任务
    fn load_generation_tasks(&mut self) -> AppResult<()> {
        for project in self.projects.values() {
            let project_dir = self.config.project_dir(&project.workspace_id, &project.id);
            for task_id in &project.generation_tasks {
                let task_file = project_dir.join("generation_tasks").join(format!("{}.json", task_id));
                if task_file.exists() {
                    let content = fs::read_to_string(task_file)?;
                    let task: GenerationTask = serde_json::from_str(&content)?;
                    self.generation_tasks.insert(task.id, task);
                }
            }
        }
        Ok(())
    }

    /// 获取配置
    pub fn config(&self) -> &AppConfig {
        &self.config
    }

    /// 获取所有工作空间
    pub fn workspaces(&self) -> Vec<&Workspace> {
        self.workspaces.values().collect()
    }

    /// 获取工作空间
    pub fn workspace(&self, id: &Uuid) -> Option<&Workspace> {
        self.workspaces.get(id)
    }

    /// 获取项目
    pub fn project(&self, id: &Uuid) -> Option<&Project> {
        self.projects.get(id)
    }

    /// 获取工作空间的项目
    pub fn workspace_projects(&self, workspace_id: &Uuid) -> Vec<&Project> {
        self.projects.values()
            .filter(|p| p.workspace_id == *workspace_id)
            .collect()
    }
}
