use crate::error::AppResult;
use crate::models::{DataSource, DataSourceType, DataColumn, DataType, ApiResponse};
use crate::AppState;
use std::path::PathBuf;
use tauri::State;
use uuid::Uuid;

/// 获取项目的数据源列表
#[tauri::command]
pub async fn get_data_sources(
    project_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Vec<DataSource>>, String> {
    let project_uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    let store = state.data_store.lock().await;

    let data_sources: Vec<DataSource> = store
        .data_sources
        .values()
        .filter(|ds| ds.project_id == project_uuid)
        .cloned()
        .collect();

    Ok(ApiResponse::success(data_sources))
}

/// 创建新的数据源
#[tauri::command]
pub async fn create_data_source(
    project_id: String,
    name: String,
    file_path: String,
    source_type: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<DataSource>, String> {
    let project_uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    let source_type = match source_type.as_str() {
        "csv" => DataSourceType::Csv,
        "excel" => DataSourceType::Excel,
        _ => return Err("不支持的数据源类型".to_string()),
    };

    let mut store = state.data_store.lock().await;

    // 检查项目是否存在
    if !store.projects.contains_key(&project_uuid) {
        return Err("项目不存在".to_string());
    }

    let data_source = DataSource {
        id: Uuid::new_v4(),
        project_id: project_uuid,
        name,
        description: None,
        file_path: PathBuf::from(file_path),
        source_type,
        encoding: None,
        has_header: true,
        delimiter: None,
        sheet_name: None,
        columns: Vec::new(),
        headers: Vec::new(),
        row_count: 0,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };

    store.data_sources.insert(data_source.id, data_source.clone());
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(data_source))
}

/// 更新数据源
#[tauri::command]
pub async fn update_data_source(
    data_source_id: String,
    name: Option<String>,
    file_path: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<DataSource>, String> {
    let id = Uuid::parse_str(&data_source_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let data_source = store
        .data_sources
        .get_mut(&id)
        .ok_or("数据源不存在")?;

    if let Some(name) = name {
        data_source.name = name;
    }

    if let Some(file_path) = file_path {
        data_source.file_path = PathBuf::from(file_path);
    }

    data_source.updated_at = chrono::Utc::now();

    let updated_data_source = data_source.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_data_source))
}

/// 删除数据源
#[tauri::command]
pub async fn delete_data_source(
    data_source_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<()>, String> {
    let id = Uuid::parse_str(&data_source_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    if store.data_sources.remove(&id).is_none() {
        return Err("数据源不存在".to_string());
    }

    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(()))
}

/// 分析数据源文件（解析头部信息和行数）
#[tauri::command]
pub async fn analyze_data_source(
    data_source_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<DataSource>, String> {
    let id = Uuid::parse_str(&data_source_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let data_source = store
        .data_sources
        .get_mut(&id)
        .ok_or("数据源不存在")?;

    // 根据文件类型分析数据源
    match data_source.source_type {
        DataSourceType::Csv => {
            analyze_csv_file(data_source).map_err(|e| e.to_string())?;
        }
        DataSourceType::Excel => {
            analyze_excel_file(data_source).map_err(|e| e.to_string())?;
        }
    }

    data_source.updated_at = chrono::Utc::now();
    let updated_data_source = data_source.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_data_source))
}

/// 分析CSV文件
fn analyze_csv_file(data_source: &mut DataSource) -> AppResult<()> {
    use std::fs::File;
    use csv::ReaderBuilder;
    use encoding_rs::*;
    use std::io::Read;

    // 读取文件并检测编码
    let mut file = File::open(&data_source.file_path)?;
    let mut buffer = Vec::new();
    file.read_to_end(&mut buffer)?;

    let (cow, encoding_used, _) = UTF_8.decode(&buffer);
    data_source.encoding = Some(encoding_used.name().to_string());

    // 解析CSV
    let mut reader = ReaderBuilder::new()
        .has_headers(true)
        .from_reader(cow.as_bytes());

    // 获取头部信息
    if let Ok(headers) = reader.headers() {
        data_source.headers = headers.iter().map(|h| h.to_string()).collect();
    }

    // 计算行数
    data_source.row_count = reader.records().count();

    Ok(())
}

/// 分析Excel文件
fn analyze_excel_file(data_source: &mut DataSource) -> AppResult<()> {
    use calamine::{Reader, open_workbook, Xlsx};

    let mut workbook: Xlsx<_> = open_workbook(&data_source.file_path)?;

    // 获取第一个工作表
    if let Some(sheet_name) = workbook.sheet_names().first() {
        if let Some(Ok(range)) = workbook.worksheet_range(sheet_name) {
            // 获取头部信息（第一行）
            if let Some(first_row) = range.rows().next() {
                data_source.headers = first_row
                    .iter()
                    .map(|cell| cell.to_string())
                    .collect();
            }

            // 计算行数（减去头部行）
            data_source.row_count = range.rows().count().saturating_sub(1);
        }
    }

    data_source.encoding = Some("UTF-8".to_string()); // Excel文件通常是UTF-8

    Ok(())
}
