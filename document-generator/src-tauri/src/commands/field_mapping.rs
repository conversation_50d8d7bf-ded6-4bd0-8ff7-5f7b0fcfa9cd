use crate::error::AppResult;
use crate::models::{FieldMapping, MappingRule, ApiResponse};
use crate::AppState;
use std::collections::HashMap;
use tauri::State;
use uuid::Uuid;

/// 获取项目的字段映射列表
#[tauri::command]
pub async fn get_field_mappings(
    project_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Vec<FieldMapping>>, String> {
    let project_uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    let store = state.data_store.lock().await;

    let mappings: Vec<FieldMapping> = store
        .field_mappings
        .values()
        .filter(|m| m.project_id == project_uuid)
        .cloned()
        .collect();

    Ok(ApiResponse::success(mappings))
}

/// 创建新的字段映射
#[tauri::command]
pub async fn create_field_mapping(
    project_id: String,
    name: String,
    data_source_id: String,
    template_id: String,
    description: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<FieldMapping>, String> {
    let project_uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    let data_source_uuid = Uuid::parse_str(&data_source_id).map_err(|e| e.to_string())?;
    let template_uuid = Uuid::parse_str(&template_id).map_err(|e| e.to_string())?;

    let mut store = state.data_store.lock().await;

    // 检查项目、数据源和模板是否存在
    if !store.projects.contains_key(&project_uuid) {
        return Err("项目不存在".to_string());
    }
    if !store.data_sources.contains_key(&data_source_uuid) {
        return Err("数据源不存在".to_string());
    }
    if !store.templates.contains_key(&template_uuid) {
        return Err("模板不存在".to_string());
    }

    let mapping = FieldMapping {
        id: Uuid::new_v4(),
        project_id: project_uuid,
        name,
        description,
        data_source_id: data_source_uuid,
        template_id: template_uuid,
        mapping_rules: Vec::new(),
        mappings: HashMap::new(),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };

    store.field_mappings.insert(mapping.id, mapping.clone());
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(mapping))
}

/// 更新字段映射
#[tauri::command]
pub async fn update_field_mapping(
    mapping_id: String,
    name: Option<String>,
    description: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<FieldMapping>, String> {
    let id = Uuid::parse_str(&mapping_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let mapping = store
        .field_mappings
        .get_mut(&id)
        .ok_or("字段映射不存在")?;

    if let Some(name) = name {
        mapping.name = name;
    }

    if let Some(description) = description {
        mapping.description = Some(description);
    }

    mapping.updated_at = chrono::Utc::now();

    let updated_mapping = mapping.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_mapping))
}

/// 删除字段映射
#[tauri::command]
pub async fn delete_field_mapping(
    mapping_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<()>, String> {
    let id = Uuid::parse_str(&mapping_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    if store.field_mappings.remove(&id).is_none() {
        return Err("字段映射不存在".to_string());
    }

    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(()))
}

/// 添加映射规则
#[tauri::command]
pub async fn add_mapping_rule(
    mapping_id: String,
    source_field: String,
    target_placeholder: String,
    default_value: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<FieldMapping>, String> {
    let id = Uuid::parse_str(&mapping_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let mapping = store
        .field_mappings
        .get_mut(&id)
        .ok_or("字段映射不存在")?;

    let rule = MappingRule {
        source_field,
        target_placeholder,
        default_value,
    };

    mapping.mapping_rules.push(rule);
    mapping.updated_at = chrono::Utc::now();

    let updated_mapping = mapping.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_mapping))
}

/// 移除映射规则
#[tauri::command]
pub async fn remove_mapping_rule(
    mapping_id: String,
    source_field: String,
    target_placeholder: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<FieldMapping>, String> {
    let id = Uuid::parse_str(&mapping_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let mapping = store
        .field_mappings
        .get_mut(&id)
        .ok_or("字段映射不存在")?;

    mapping.mapping_rules.retain(|rule| {
        !(rule.source_field == source_field && rule.target_placeholder == target_placeholder)
    });

    mapping.updated_at = chrono::Utc::now();

    let updated_mapping = mapping.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_mapping))
}

/// 批量更新映射规则
#[tauri::command]
pub async fn update_mapping_rules(
    mapping_id: String,
    rules: Vec<MappingRule>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<FieldMapping>, String> {
    let id = Uuid::parse_str(&mapping_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let mapping = store
        .field_mappings
        .get_mut(&id)
        .ok_or("字段映射不存在")?;

    mapping.mapping_rules = rules;
    mapping.updated_at = chrono::Utc::now();

    let updated_mapping = mapping.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_mapping))
}
