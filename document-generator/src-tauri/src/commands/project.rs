use crate::error::{AppError, AppResult};
use crate::models::{ApiResponse, Project};
use crate::AppState;
use chrono::Utc;
use std::fs;
use tauri::State;
use uuid::Uuid;

/// 获取工作空间的所有项目
#[tauri::command]
pub async fn get_projects(
    workspace_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Vec<Project>>, String> {
    let data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    
    let workspace_uuid = match Uuid::parse_str(&workspace_id) {
        Ok(id) => id,
        Err(_) => return Ok(ApiResponse::error("无效的工作空间ID".to_string())),
    };

    let projects = data_store.workspace_projects(&workspace_uuid)
        .into_iter()
        .cloned()
        .collect();
    
    Ok(ApiResponse::success(projects))
}

/// 创建新项目
#[tauri::command]
pub async fn create_project(
    workspace_id: String,
    name: String,
    description: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Project>, String> {
    let mut data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    
    let workspace_uuid = match Uuid::parse_str(&workspace_id) {
        Ok(id) => id,
        Err(_) => return Ok(ApiResponse::error("无效的工作空间ID".to_string())),
    };

    // 验证工作空间是否存在
    let mut workspace = match data_store.workspaces.get(&workspace_uuid).cloned() {
        Some(workspace) => workspace,
        None => return Ok(ApiResponse::error("工作空间不存在".to_string())),
    };

    // 验证输入
    if name.trim().is_empty() {
        return Ok(ApiResponse::error("项目名称不能为空".to_string()));
    }

    let project = Project {
        id: Uuid::new_v4(),
        name: name.trim().to_string(),
        description: description.map(|d| d.trim().to_string()).filter(|d| !d.is_empty()),
        workspace_id: workspace_uuid,
        data_sources: Vec::new(),
        templates: Vec::new(),
        field_mappings: Vec::new(),
        generation_tasks: Vec::new(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    // 创建项目目录结构
    let project_dir = data_store.config().project_dir(&workspace_uuid, &project.id);
    let data_sources_dir = data_store.config().data_sources_dir(&workspace_uuid, &project.id);
    let templates_dir = data_store.config().templates_dir(&workspace_uuid, &project.id);
    let output_dir = data_store.config().output_dir(&workspace_uuid, &project.id);

    for dir in [&project_dir, &data_sources_dir, &templates_dir, &output_dir] {
        if let Err(e) = fs::create_dir_all(dir) {
            return Ok(ApiResponse::error(format!("创建项目目录失败: {}", e)));
        }
    }

    // 创建其他必要的子目录
    let field_mappings_dir = project_dir.join("field_mappings");
    let generation_tasks_dir = project_dir.join("generation_tasks");
    
    for dir in [&field_mappings_dir, &generation_tasks_dir] {
        if let Err(e) = fs::create_dir_all(dir) {
            return Ok(ApiResponse::error(format!("创建项目子目录失败: {}", e)));
        }
    }

    // 保存项目配置
    let project_file = project_dir.join("project.json");
    let content = match serde_json::to_string_pretty(&project) {
        Ok(content) => content,
        Err(e) => return Ok(ApiResponse::error(format!("序列化项目配置失败: {}", e))),
    };

    if let Err(e) = fs::write(project_file, content) {
        return Ok(ApiResponse::error(format!("保存项目配置失败: {}", e)));
    }

    // 更新工作空间的项目列表
    workspace.projects.push(project.id);
    workspace.updated_at = Utc::now();

    // 保存更新后的工作空间配置
    let workspace_dir = data_store.config().workspace_dir(&workspace_uuid);
    let workspace_file = workspace_dir.join("workspace.json");
    let workspace_content = match serde_json::to_string_pretty(&workspace) {
        Ok(content) => content,
        Err(e) => return Ok(ApiResponse::error(format!("序列化工作空间配置失败: {}", e))),
    };

    if let Err(e) = fs::write(workspace_file, workspace_content) {
        return Ok(ApiResponse::error(format!("保存工作空间配置失败: {}", e)));
    }

    // 添加到内存中
    data_store.projects.insert(project.id, project.clone());
    data_store.workspaces.insert(workspace.id, workspace);

    Ok(ApiResponse::success(project))
}

/// 更新项目
#[tauri::command]
pub async fn update_project(
    id: String,
    name: Option<String>,
    description: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Project>, String> {
    let mut data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    
    let project_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Ok(ApiResponse::error("无效的项目ID".to_string())),
    };

    let mut project = match data_store.projects.get(&project_id).cloned() {
        Some(project) => project,
        None => return Ok(ApiResponse::error("项目不存在".to_string())),
    };

    // 更新字段
    if let Some(name) = name {
        if name.trim().is_empty() {
            return Ok(ApiResponse::error("项目名称不能为空".to_string()));
        }
        project.name = name.trim().to_string();
    }

    if let Some(description) = description {
        project.description = if description.trim().is_empty() {
            None
        } else {
            Some(description.trim().to_string())
        };
    }

    project.updated_at = Utc::now();

    // 保存更新后的配置
    let project_dir = data_store.config().project_dir(&project.workspace_id, &project.id);
    let project_file = project_dir.join("project.json");
    let content = match serde_json::to_string_pretty(&project) {
        Ok(content) => content,
        Err(e) => return Ok(ApiResponse::error(format!("序列化项目配置失败: {}", e))),
    };

    if let Err(e) = fs::write(project_file, content) {
        return Ok(ApiResponse::error(format!("保存项目配置失败: {}", e)));
    }

    // 更新内存中的数据
    data_store.projects.insert(project.id, project.clone());

    Ok(ApiResponse::success(project))
}

/// 删除项目
#[tauri::command]
pub async fn delete_project(
    id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<bool>, String> {
    let mut data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    
    let project_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Ok(ApiResponse::error("无效的项目ID".to_string())),
    };

    let project = match data_store.projects.get(&project_id) {
        Some(project) => project.clone(),
        None => return Ok(ApiResponse::error("项目不存在".to_string())),
    };

    // 检查是否有正在运行的生成任务
    let has_running_tasks = data_store.generation_tasks.values()
        .any(|task| task.project_id == project_id && 
             matches!(task.status, crate::models::TaskStatus::Running));
    
    if has_running_tasks {
        return Ok(ApiResponse::error("无法删除有正在运行任务的项目，请先停止所有任务".to_string()));
    }

    // 删除项目目录
    let project_dir = data_store.config().project_dir(&project.workspace_id, &project.id);
    if project_dir.exists() {
        if let Err(e) = fs::remove_dir_all(&project_dir) {
            return Ok(ApiResponse::error(format!("删除项目目录失败: {}", e)));
        }
    }

    // 从工作空间的项目列表中移除
    if let Some(mut workspace) = data_store.workspaces.get(&project.workspace_id).cloned() {
        workspace.projects.retain(|&id| id != project_id);
        workspace.updated_at = Utc::now();

        // 保存更新后的工作空间配置
        let workspace_dir = data_store.config().workspace_dir(&workspace.id);
        let workspace_file = workspace_dir.join("workspace.json");
        let workspace_content = match serde_json::to_string_pretty(&workspace) {
            Ok(content) => content,
            Err(e) => return Ok(ApiResponse::error(format!("序列化工作空间配置失败: {}", e))),
        };

        if let Err(e) = fs::write(workspace_file, workspace_content) {
            return Ok(ApiResponse::error(format!("保存工作空间配置失败: {}", e)));
        }

        data_store.workspaces.insert(workspace.id, workspace);
    }

    // 从内存中移除相关数据
    data_store.projects.remove(&project_id);
    
    // 移除相关的数据源、模板、字段映射和生成任务
    data_store.data_sources.retain(|_, ds| ds.project_id != project_id);
    data_store.templates.retain(|_, t| t.project_id != project_id);
    data_store.field_mappings.retain(|_, fm| fm.project_id != project_id);
    data_store.generation_tasks.retain(|_, gt| gt.project_id != project_id);

    Ok(ApiResponse::success(true))
}
