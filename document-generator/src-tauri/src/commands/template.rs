use crate::error::AppResult;
use crate::models::{Template, Placeholder, PlaceholderPosition, DataType, ApiResponse};
use crate::AppState;
use std::path::PathBuf;
use tauri::State;
use uuid::Uuid;
use regex::Regex;

/// 获取项目的模板列表
#[tauri::command]
pub async fn get_templates(
    project_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Vec<Template>>, String> {
    let project_uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    let store = state.data_store.lock().await;

    let templates: Vec<Template> = store
        .templates
        .values()
        .filter(|t| t.project_id == project_uuid)
        .cloned()
        .collect();

    Ok(ApiResponse::success(templates))
}

/// 创建新的模板
#[tauri::command]
pub async fn create_template(
    project_id: String,
    name: String,
    file_path: String,
    description: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Template>, String> {
    let project_uuid = Uuid::parse_str(&project_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    // 检查项目是否存在
    if !store.projects.contains_key(&project_uuid) {
        return Err("项目不存在".to_string());
    }

    let template = Template {
        id: Uuid::new_v4(),
        project_id: project_uuid,
        name,
        description,
        file_path: PathBuf::from(file_path),
        placeholders: Vec::new(),
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };

    store.templates.insert(template.id, template.clone());
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(template))
}

/// 更新模板
#[tauri::command]
pub async fn update_template(
    template_id: String,
    name: Option<String>,
    description: Option<String>,
    file_path: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Template>, String> {
    let id = Uuid::parse_str(&template_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let template = store
        .templates
        .get_mut(&id)
        .ok_or("模板不存在")?;

    if let Some(name) = name {
        template.name = name;
    }

    if let Some(description) = description {
        template.description = Some(description);
    }

    if let Some(file_path) = file_path {
        template.file_path = PathBuf::from(file_path);
    }

    template.updated_at = chrono::Utc::now();

    let updated_template = template.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_template))
}

/// 删除模板
#[tauri::command]
pub async fn delete_template(
    template_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<()>, String> {
    let id = Uuid::parse_str(&template_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    if store.templates.remove(&id).is_none() {
        return Err("模板不存在".to_string());
    }

    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(()))
}

/// 分析模板文件（提取占位符）
#[tauri::command]
pub async fn analyze_template(
    template_id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Template>, String> {
    let id = Uuid::parse_str(&template_id).map_err(|e| e.to_string())?;
    let mut store = state.data_store.lock().await;

    let template = store
        .templates
        .get_mut(&id)
        .ok_or("模板不存在")?;

    // 分析Word文档模板，提取占位符
    extract_placeholders_from_docx(template).map_err(|e| e.to_string())?;

    template.updated_at = chrono::Utc::now();
    let updated_template = template.clone();
    store.save_to_file().map_err(|e| e.to_string())?;

    Ok(ApiResponse::success(updated_template))
}

/// 从Word文档中提取占位符
fn extract_placeholders_from_docx(template: &mut Template) -> AppResult<()> {
    use std::fs::File;
    use std::io::Read;
    use zip::ZipArchive;
    use regex::Regex;

    let file = File::open(&template.file_path)?;
    let mut archive = ZipArchive::new(file)?;

    // 读取document.xml文件
    let mut document_xml = String::new();
    if let Ok(mut file) = archive.by_name("word/document.xml") {
        file.read_to_string(&mut document_xml)?;
    }

    // 使用正则表达式提取占位符
    // 支持 {{placeholder}} 和 ${placeholder} 格式
    let re = Regex::new(r"\{\{([^}]+)\}\}|\$\{([^}]+)\}").unwrap();
    let mut placeholders = Vec::new();

    for cap in re.captures_iter(&document_xml) {
        let placeholder = cap.get(1).or_else(|| cap.get(2))
            .map(|m| m.as_str().trim().to_string());

        if let Some(placeholder) = placeholder {
            if !placeholders.contains(&placeholder) {
                placeholders.push(placeholder);
            }
        }
    }

    template.placeholders = placeholders;

    Ok(())
}
