use crate::error::{AppError, AppResult};
use crate::models::{ApiResponse, Workspace};
use crate::AppState;
use chrono::Utc;
use std::fs;
use tauri::State;
use uuid::Uuid;

/// 获取所有工作空间
#[tauri::command]
pub async fn get_workspaces(state: State<'_, AppState>) -> Result<ApiResponse<Vec<Workspace>>, String> {
    let data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    let workspaces = data_store.workspaces().into_iter().cloned().collect();
    Ok(ApiResponse::success(workspaces))
}

/// 创建新工作空间
#[tauri::command]
pub async fn create_workspace(
    name: String,
    path: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Workspace>, String> {
    let mut data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    
    // 验证输入
    if name.trim().is_empty() {
        return Ok(ApiResponse::error("工作空间名称不能为空".to_string()));
    }
    
    if path.trim().is_empty() {
        return Ok(ApiResponse::error("工作空间路径不能为空".to_string()));
    }

    // 检查路径是否存在
    if !std::path::Path::new(&path).exists() {
        return Ok(ApiResponse::error("指定的路径不存在".to_string()));
    }

    let workspace = Workspace {
        id: Uuid::new_v4(),
        name: name.trim().to_string(),
        path: path.trim().to_string(),
        projects: Vec::new(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    // 创建工作空间目录
    let workspace_dir = data_store.config().workspace_dir(&workspace.id);
    if let Err(e) = fs::create_dir_all(&workspace_dir) {
        return Ok(ApiResponse::error(format!("创建工作空间目录失败: {}", e)));
    }

    // 保存工作空间配置
    let workspace_file = workspace_dir.join("workspace.json");
    let content = match serde_json::to_string_pretty(&workspace) {
        Ok(content) => content,
        Err(e) => return Ok(ApiResponse::error(format!("序列化工作空间配置失败: {}", e))),
    };

    if let Err(e) = fs::write(workspace_file, content) {
        return Ok(ApiResponse::error(format!("保存工作空间配置失败: {}", e)));
    }

    // 添加到内存中
    data_store.workspaces.insert(workspace.id, workspace.clone());

    Ok(ApiResponse::success(workspace))
}

/// 更新工作空间
#[tauri::command]
pub async fn update_workspace(
    id: String,
    name: Option<String>,
    path: Option<String>,
    state: State<'_, AppState>,
) -> Result<ApiResponse<Workspace>, String> {
    let mut data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    
    let workspace_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Ok(ApiResponse::error("无效的工作空间ID".to_string())),
    };

    let mut workspace = match data_store.workspaces.get(&workspace_id).cloned() {
        Some(workspace) => workspace,
        None => return Ok(ApiResponse::error("工作空间不存在".to_string())),
    };

    // 更新字段
    if let Some(name) = name {
        if name.trim().is_empty() {
            return Ok(ApiResponse::error("工作空间名称不能为空".to_string()));
        }
        workspace.name = name.trim().to_string();
    }

    if let Some(path) = path {
        if path.trim().is_empty() {
            return Ok(ApiResponse::error("工作空间路径不能为空".to_string()));
        }
        if !std::path::Path::new(&path).exists() {
            return Ok(ApiResponse::error("指定的路径不存在".to_string()));
        }
        workspace.path = path.trim().to_string();
    }

    workspace.updated_at = Utc::now();

    // 保存更新后的配置
    let workspace_dir = data_store.config().workspace_dir(&workspace.id);
    let workspace_file = workspace_dir.join("workspace.json");
    let content = match serde_json::to_string_pretty(&workspace) {
        Ok(content) => content,
        Err(e) => return Ok(ApiResponse::error(format!("序列化工作空间配置失败: {}", e))),
    };

    if let Err(e) = fs::write(workspace_file, content) {
        return Ok(ApiResponse::error(format!("保存工作空间配置失败: {}", e)));
    }

    // 更新内存中的数据
    data_store.workspaces.insert(workspace.id, workspace.clone());

    Ok(ApiResponse::success(workspace))
}

/// 删除工作空间
#[tauri::command]
pub async fn delete_workspace(
    id: String,
    state: State<'_, AppState>,
) -> Result<ApiResponse<bool>, String> {
    let mut data_store = state.data_store.lock().map_err(|e| e.to_string())?;
    
    let workspace_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Ok(ApiResponse::error("无效的工作空间ID".to_string())),
    };

    if !data_store.workspaces.contains_key(&workspace_id) {
        return Ok(ApiResponse::error("工作空间不存在".to_string()));
    }

    // 检查是否有关联的项目
    let has_projects = data_store.projects.values()
        .any(|p| p.workspace_id == workspace_id);
    
    if has_projects {
        return Ok(ApiResponse::error("无法删除包含项目的工作空间，请先删除所有项目".to_string()));
    }

    // 删除工作空间目录
    let workspace_dir = data_store.config().workspace_dir(&workspace_id);
    if workspace_dir.exists() {
        if let Err(e) = fs::remove_dir_all(&workspace_dir) {
            return Ok(ApiResponse::error(format!("删除工作空间目录失败: {}", e)));
        }
    }

    // 从内存中移除
    data_store.workspaces.remove(&workspace_id);

    Ok(ApiResponse::success(true))
}
